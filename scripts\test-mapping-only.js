// 测试模型映射逻辑（不需要数据库连接）

// 模型映射配置（与ai-points-service.ts保持一致）
const MODEL_MAPPING = {
  // DeepSeek 模型映射到 loomrun_1.2ds
  'deepseek-chat': 'loomrun_1.2ds',
  'deepseek-coder': 'loomrun_1.2ds',
  'deepseek-reasoner': 'loomrun_1.2ds',
  
  // 豆包模型映射到 loomrun_1.6db
  'doubao-lite-4k': 'loomrun_1.6db',
  'doubao-pro-4k': 'loomrun_1.6db',
  'doubao-pro-32k': 'loomrun_1.6db',
  'doubao-pro-128k': 'loomrun_1.6db',
  'doubao-seed-1-6-250615': 'loomrun_1.6db', // 当前使用的豆包模型
  'doubao-seed-1-6-250616': 'loomrun_1.6db',
  'doubao-seed-1-6-250617': 'loomrun_1.6db',
  
  // 其他可能的豆包模型变体
  'ep-20241230140251-8xqzx': 'loomrun_1.6db',
  'ep-20241230140251-xxxxx': 'loomrun_1.6db',
};

// 获取映射后的模型key
const getMappedModelKey = (originalModelKey) => {
  // 1. 直接映射
  if (MODEL_MAPPING[originalModelKey]) {
    console.log(`🔄 模型映射: ${originalModelKey} → ${MODEL_MAPPING[originalModelKey]}`);
    return MODEL_MAPPING[originalModelKey];
  }
  
  // 2. 模糊匹配 - 豆包模型
  if (originalModelKey.includes('doubao') || originalModelKey.includes('ep-')) {
    console.log(`🔄 模糊匹配豆包模型: ${originalModelKey} → loomrun_1.6db`);
    return 'loomrun_1.6db';
  }
  
  // 3. 模糊匹配 - DeepSeek模型
  if (originalModelKey.includes('deepseek')) {
    console.log(`🔄 模糊匹配DeepSeek模型: ${originalModelKey} → loomrun_1.2ds`);
    return 'loomrun_1.2ds';
  }
  
  // 4. 默认情况 - 返回原始key
  console.log(`⚠️ 未找到模型映射: ${originalModelKey}，使用原始key`);
  return originalModelKey;
};

function testMappingLogic() {
  console.log('🔍 测试模型映射逻辑:\n');
  
  const testCases = [
    {
      input: 'doubao-seed-1-6-250615',
      expected: 'loomrun_1.6db',
      description: '当前使用的豆包模型'
    },
    {
      input: 'deepseek-chat',
      expected: 'loomrun_1.2ds',
      description: 'DeepSeek基础模型'
    },
    {
      input: 'doubao-pro-4k',
      expected: 'loomrun_1.6db',
      description: '豆包专业版'
    },
    {
      input: 'ep-20241230140251-8xqzx',
      expected: 'loomrun_1.6db',
      description: '豆包endpoint模型'
    },
    {
      input: 'doubao-new-version-123',
      expected: 'loomrun_1.6db',
      description: '未知豆包模型（模糊匹配）'
    },
    {
      input: 'deepseek-v2-chat',
      expected: 'loomrun_1.2ds',
      description: '未知DeepSeek模型（模糊匹配）'
    },
    {
      input: 'some-unknown-model',
      expected: 'some-unknown-model',
      description: '完全未知模型'
    }
  ];

  let passedTests = 0;
  let totalTests = testCases.length;

  testCases.forEach((testCase, index) => {
    console.log(`📋 测试 ${index + 1}: ${testCase.description}`);
    console.log(`   输入: ${testCase.input}`);
    
    const result = getMappedModelKey(testCase.input);
    const passed = result === testCase.expected;
    
    console.log(`   输出: ${result}`);
    console.log(`   期望: ${testCase.expected}`);
    console.log(`   结果: ${passed ? '✅ 通过' : '❌ 失败'}\n`);
    
    if (passed) passedTests++;
  });

  console.log(`📊 测试结果: ${passedTests}/${totalTests} 通过`);
  
  if (passedTests === totalTests) {
    console.log('🎉 所有测试通过！映射逻辑正确');
  } else {
    console.log('⚠️ 部分测试失败，需要检查映射逻辑');
  }

  // 特别测试关键模型
  console.log('\n🎯 关键模型测试:');
  
  const keyModels = [
    'doubao-seed-1-6-250615', // 日志中显示的实际模型
    'deepseek-chat'
  ];

  keyModels.forEach(model => {
    const mapped = getMappedModelKey(model);
    console.log(`  ${model} → ${mapped}`);
    
    // 模拟积分查询结果
    const mockPointsCost = mapped === 'loomrun_1.6db' ? 4 : 
                          mapped === 'loomrun_1.2ds' ? 2 : 0;
    console.log(`    预期积分消耗: ${mockPointsCost}积分`);
  });
}

// 运行测试
testMappingLogic();
