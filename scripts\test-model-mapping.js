const mysql = require('mysql2/promise');

// 模型映射配置（与ai-points-service.ts保持一致）
const MODEL_MAPPING = {
  // DeepSeek 模型映射到 loomrun_1.2ds
  'deepseek-chat': 'loomrun_1.2ds',
  'deepseek-coder': 'loomrun_1.2ds',
  'deepseek-reasoner': 'loomrun_1.2ds',
  
  // 豆包模型映射到 loomrun_1.6db
  'doubao-lite-4k': 'loomrun_1.6db',
  'doubao-pro-4k': 'loomrun_1.6db',
  'doubao-pro-32k': 'loomrun_1.6db',
  'doubao-pro-128k': 'loomrun_1.6db',
  'doubao-seed-1-6-250615': 'loomrun_1.6db', // 当前使用的豆包模型
  'doubao-seed-1-6-250616': 'loomrun_1.6db',
  'doubao-seed-1-6-250617': 'loomrun_1.6db',
  
  // 其他可能的豆包模型变体
  'ep-20241230140251-8xqzx': 'loomrun_1.6db',
  'ep-20241230140251-xxxxx': 'loomrun_1.6db',
};

// 获取映射后的模型key
const getMappedModelKey = (originalModelKey) => {
  // 1. 直接映射
  if (MODEL_MAPPING[originalModelKey]) {
    console.log(`🔄 模型映射: ${originalModelKey} → ${MODEL_MAPPING[originalModelKey]}`);
    return MODEL_MAPPING[originalModelKey];
  }
  
  // 2. 模糊匹配 - 豆包模型
  if (originalModelKey.includes('doubao') || originalModelKey.includes('ep-')) {
    console.log(`🔄 模糊匹配豆包模型: ${originalModelKey} → loomrun_1.6db`);
    return 'loomrun_1.6db';
  }
  
  // 3. 模糊匹配 - DeepSeek模型
  if (originalModelKey.includes('deepseek')) {
    console.log(`🔄 模糊匹配DeepSeek模型: ${originalModelKey} → loomrun_1.2ds`);
    return 'loomrun_1.2ds';
  }
  
  // 4. 默认情况 - 返回原始key
  console.log(`⚠️ 未找到模型映射: ${originalModelKey}，使用原始key`);
  return originalModelKey;
};

async function testModelMapping() {
  let connection;
  
  try {
    // 创建数据库连接
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || 'root',
      database: process.env.DB_NAME || 'loomrun',
      charset: 'utf8mb4'
    });

    console.log('🔗 数据库连接成功');

    // 1. 查看数据库中的AI模型配置
    console.log('\n🤖 数据库中的AI模型配置:');
    const [aiModels] = await connection.execute(
      'SELECT model_key, model_name, points_per_request, is_active FROM ai_models ORDER BY display_order'
    );
    
    aiModels.forEach(model => {
      console.log(`  - ${model.model_key}: ${model.model_name} (${model.points_per_request}积分) ${model.is_active ? '✅' : '❌'}`);
    });

    // 2. 测试各种模型映射
    console.log('\n🔍 测试模型映射逻辑:');
    
    const testModels = [
      'doubao-seed-1-6-250615', // 当前使用的豆包模型
      'deepseek-chat',
      'doubao-pro-4k',
      'ep-20241230140251-8xqzx',
      'some-unknown-model',
      'doubao-new-version-123',
      'deepseek-v2-chat'
    ];

    for (const testModel of testModels) {
      console.log(`\n📋 测试模型: ${testModel}`);
      const mappedKey = getMappedModelKey(testModel);
      
      // 查询映射后的模型配置
      const [results] = await connection.execute(
        'SELECT model_key, model_name, points_per_request FROM ai_models WHERE model_key = ? AND is_active = 1',
        [mappedKey]
      );
      
      if (results.length > 0) {
        const model = results[0];
        console.log(`  ✅ 找到配置: ${model.model_key} (${model.model_name}) - ${model.points_per_request}积分`);
      } else {
        console.log(`  ❌ 未找到配置: ${mappedKey}`);
        
        // 如果映射后没找到，尝试原始key
        if (mappedKey !== testModel) {
          const [fallbackResults] = await connection.execute(
            'SELECT model_key, model_name, points_per_request FROM ai_models WHERE model_key = ? AND is_active = 1',
            [testModel]
          );
          
          if (fallbackResults.length > 0) {
            const model = fallbackResults[0];
            console.log(`  🔄 原始key找到: ${model.model_key} (${model.model_name}) - ${model.points_per_request}积分`);
          } else {
            console.log(`  ❌ 原始key也未找到: ${testModel}`);
          }
        }
      }
    }

    // 3. 模拟实际的积分查询函数
    console.log('\n🎯 模拟实际积分查询:');
    
    const getModelPointsCost = async (modelKey) => {
      try {
        const mappedModelKey = getMappedModelKey(modelKey);
        
        const [results] = await connection.execute(
          'SELECT points_per_request FROM ai_models WHERE model_key = ? AND is_active = 1',
          [mappedModelKey]
        );
        
        if (results.length > 0) {
          return results[0].points_per_request;
        } else {
          // 如果映射后的key没找到，尝试用原始key再查一次
          if (mappedModelKey !== modelKey) {
            const [fallbackResults] = await connection.execute(
              'SELECT points_per_request FROM ai_models WHERE model_key = ? AND is_active = 1',
              [modelKey]
            );
            
            if (fallbackResults.length > 0) {
              return fallbackResults[0].points_per_request;
            }
          }
          
          return 0;
        }
      } catch (error) {
        console.error('查询失败:', error);
        return 0;
      }
    };

    // 测试关键模型
    const keyModels = ['doubao-seed-1-6-250615', 'deepseek-chat'];
    
    for (const model of keyModels) {
      const pointsCost = await getModelPointsCost(model);
      console.log(`  ${model}: ${pointsCost}积分 ${pointsCost > 0 ? '✅' : '❌'}`);
    }

    console.log('\n✅ 模型映射测试完成');

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 数据库连接已关闭');
    }
  }
}

// 运行测试
testModelMapping().catch(console.error);
