// 测试小写格式的模型名称映射
console.log('🔍 测试小写格式模型名称映射...\n');

// 模型映射配置（与API保持一致）
const MODEL_DISPLAY_MAPPING = {
  // DeepSeek 模型映射
  'deepseek-chat': 'loomrun_1.2ds',
  'deepseek-coder': 'loomrun_1.2ds',
  'deepseek-reasoner': 'loomrun_1.2ds',
  
  // 豆包模型映射
  'doubao-seed-1-6-250615': 'loomrun_1.6db',
  'doubao-seed-1-6-250616': 'loomrun_1.6db',
  'doubao-seed-1-6-250617': 'loomrun_1.6db',
  
  // 数据库中的模型key直接映射
  'loomrun_1.2ds': 'loomrun_1.2ds',
  'loomrun_1.6db': 'loomrun_1.6db',
};

const getDisplayModelName = (originalModelName) => {
  // 1. 直接映射
  if (MODEL_DISPLAY_MAPPING[originalModelName]) {
    return MODEL_DISPLAY_MAPPING[originalModelName];
  }
  
  // 2. 模糊匹配 - 豆包模型
  if (originalModelName.includes('doubao') || originalModelName.includes('ep-')) {
    return 'loomrun_1.6db';
  }
  
  // 3. 模糊匹配 - DeepSeek模型
  if (originalModelName.includes('deepseek')) {
    return 'loomrun_1.2ds';
  }
  
  // 4. 默认情况 - 返回原始名称
  return originalModelName;
};

console.log('✅ 已完成的修复:');
console.log('');

console.log('1. 📡 积分历史API (app/api/points/history/route.ts):');
console.log('   ✅ 更新映射规则为小写格式');
console.log('   ✅ deepseek-chat → loomrun_1.2ds');
console.log('   ✅ doubao-seed-1-6-250615 → loomrun_1.6db');
console.log('');

console.log('2. 📡 消耗详情API (app/api/points/consumption/route.ts):');
console.log('   ✅ 添加了相同的模型名称映射逻辑');
console.log('   ✅ 返回 displayTransactionDescription 字段');
console.log('   ✅ 支持消耗详情页面的模型名称转换');
console.log('');

console.log('3. 🎨 前端页面修复:');
console.log('   ✅ app/points-pro/page.tsx - 积分历史 (第620行)');
console.log('   ✅ app/points-pro/page.tsx - 消耗详情 (第688行)');
console.log('   ✅ app/points-integrated/page.tsx - 第250行');
console.log('   ✅ app/points/page.tsx - 第305行');
console.log('   ✅ app/test-points/page.tsx - 第201行');
console.log('');

console.log('4. 🔧 TypeScript接口更新:');
console.log('   ✅ PointsTransaction 接口添加 displayDescription 字段');
console.log('   ✅ ConsumptionRecord 接口添加 displayTransactionDescription 字段');
console.log('');

// 测试映射逻辑
console.log('🧪 测试映射逻辑:');
console.log('');

const testCases = [
  {
    input: 'deepseek-chat',
    expected: 'loomrun_1.2ds',
    description: 'DeepSeek基础模型'
  },
  {
    input: 'doubao-seed-1-6-250615',
    expected: 'loomrun_1.6db',
    description: '当前使用的豆包模型'
  },
  {
    input: 'loomrun_1.2ds',
    expected: 'loomrun_1.2ds',
    description: '数据库中的DeepSeek模型key'
  },
  {
    input: 'loomrun_1.6db',
    expected: 'loomrun_1.6db',
    description: '数据库中的豆包模型key'
  }
];

let passedTests = 0;
let totalTests = testCases.length;

testCases.forEach((testCase, index) => {
  console.log(`📋 测试 ${index + 1}: ${testCase.description}`);
  console.log(`   输入: ${testCase.input}`);
  
  const result = getDisplayModelName(testCase.input);
  const passed = result === testCase.expected;
  
  console.log(`   输出: ${result}`);
  console.log(`   期望: ${testCase.expected}`);
  console.log(`   结果: ${passed ? '✅ 通过' : '❌ 失败'}\n`);
  
  if (passed) passedTests++;
});

console.log(`📊 测试结果: ${passedTests}/${totalTests} 通过`);

if (passedTests === totalTests) {
  console.log('🎉 所有测试通过！小写格式映射逻辑正确');
} else {
  console.log('⚠️ 部分测试失败，需要检查映射逻辑');
}

// 测试描述替换
console.log('\n🔍 测试描述替换:');

const descriptionTests = [
  {
    original: 'AI模型 deepseek-chat 请求消耗',
    expected: 'AI模型 loomrun_1.2ds 请求消耗',
    description: 'DeepSeek模型描述'
  },
  {
    original: 'AI模型 doubao-seed-1-6-250615 请求消耗',
    expected: 'AI模型 loomrun_1.6db 请求消耗',
    description: '豆包模型描述'
  }
];

descriptionTests.forEach((test, index) => {
  console.log(`📝 描述测试 ${index + 1}: ${test.description}`);
  console.log(`   原始: ${test.original}`);
  
  // 模拟描述替换逻辑
  let result = test.original.replace(
    /AI模型\s*([\w\-\.]+)/g,
    (match, modelName) => {
      const displayModelName = getDisplayModelName(modelName);
      return `AI模型 ${displayModelName}`;
    }
  );
  
  const passed = result === test.expected;
  
  console.log(`   结果: ${result}`);
  console.log(`   期望: ${test.expected}`);
  console.log(`   状态: ${passed ? '✅ 通过' : '❌ 失败'}\n`);
});

console.log('🎯 预期结果:');
console.log('');
console.log('积分历史页面应该显示:');
console.log('  ✅ AI模型 loomrun_1.2ds 请求消耗');
console.log('  ✅ AI模型 loomrun_1.6db 请求消耗');
console.log('');
console.log('消耗详情页面应该显示:');
console.log('  ✅ AI模型 loomrun_1.2ds 请求消耗');
console.log('  ✅ AI模型 loomrun_1.6db 请求消耗');
console.log('');

console.log('✅ 小写格式映射测试完成');
console.log('现在所有页面都应该显示统一的小写格式模型名称！🎉');
