// 测试完整的积分扣除和显示流程
const fetch = require('node-fetch');

async function testFullFlow() {
  console.log('🔍 测试完整的积分扣除和显示流程...\n');
  
  console.log('📋 问题诊断步骤:');
  console.log('1. 检查AI请求是否正确扣除积分');
  console.log('2. 检查积分记录是否包含正确的metadata');
  console.log('3. 检查API是否正确处理模型名称映射');
  console.log('4. 检查前端是否使用displayDescription字段');
  console.log('');
  
  console.log('🔍 可能的问题原因:');
  console.log('');
  
  console.log('❌ 问题1: AI请求没有正确扣除积分');
  console.log('   - 检查: 模型映射逻辑是否生效');
  console.log('   - 检查: doubao-seed-1-6-250615 是否映射到 loomrun_1.6db');
  console.log('   - 检查: 数据库中ai_models表是否有正确配置');
  console.log('');
  
  console.log('❌ 问题2: 积分记录没有metadata');
  console.log('   - 检查: chargePointsForAIRequest函数是否正确传递metadata');
  console.log('   - 检查: consumePointsWithPriority函数是否正确保存metadata');
  console.log('   - 检查: 数据库中points_transactions表的metadata字段');
  console.log('');
  
  console.log('❌ 问题3: API处理逻辑问题');
  console.log('   - 检查: /api/points/history是否正确解析metadata');
  console.log('   - 检查: 模型名称替换逻辑是否正确执行');
  console.log('   - 检查: displayDescription字段是否正确生成');
  console.log('');
  
  console.log('❌ 问题4: 前端显示问题');
  console.log('   - 检查: 前端是否使用displayDescription而不是description');
  console.log('   - 检查: TypeScript接口是否正确定义');
  console.log('   - 检查: 组件是否正确渲染新字段');
  console.log('');
  
  console.log('🛠️ 调试建议:');
  console.log('');
  
  console.log('1. 发起一次新的AI请求，观察控制台日志:');
  console.log('   - 应该看到: "🔄 模型映射: doubao-seed-1-6-250615 → loomrun_1.6db"');
  console.log('   - 应该看到: "✅ 找到模型积分配置: loomrun_1.6db = 4积分"');
  console.log('   - 应该看到: "✅ AI请求积分扣除成功"');
  console.log('');
  
  console.log('2. 访问积分页面，观察控制台日志:');
  console.log('   - 应该看到: "🔍 [DEBUG] 处理描述: ..."');
  console.log('   - 应该看到: "🔍 [DEBUG] metadata: ..."');
  console.log('   - 应该看到: "🔍 [DEBUG] 模型映射: ..."');
  console.log('   - 应该看到: "🔍 [DEBUG] 处理后描述: ..."');
  console.log('');
  
  console.log('3. 检查网络请求:');
  console.log('   - 打开浏览器开发者工具');
  console.log('   - 查看 /api/points/history 的响应');
  console.log('   - 确认返回的数据中有 displayDescription 字段');
  console.log('');
  
  console.log('4. 如果仍然不工作，可能需要:');
  console.log('   - 清除浏览器缓存');
  console.log('   - 重启开发服务器');
  console.log('   - 检查数据库中的实际数据');
  console.log('');
  
  console.log('🎯 预期结果:');
  console.log('发起AI请求后，积分页面应该显示:');
  console.log('  "AI模型 LoomRun 1.6DB 请求消耗" 而不是');
  console.log('  "AI模型 doubao-seed-1-6-250615 请求消耗"');
  console.log('');
  
  console.log('✅ 测试指南完成');
  console.log('请按照上述步骤进行调试，并观察控制台日志输出');
}

testFullFlow();
