<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试积分消耗</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fafafa;
        }
        .test-section h3 {
            color: #555;
            margin-top: 0;
        }
        .model-info {
            background: #e8f4fd;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .expected {
            color: #28a745;
            font-weight: bold;
        }
        .warning {
            color: #dc3545;
            font-weight: bold;
        }
        .code {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            border-left: 4px solid #007bff;
            margin: 10px 0;
        }
        .step {
            margin: 15px 0;
            padding: 10px;
            background: white;
            border-radius: 5px;
            border-left: 3px solid #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 积分消耗系统测试</h1>
        
        <div class="test-section">
            <h3>📋 当前问题分析</h3>
            <p>根据日志分析，AI请求没有扣除积分的原因是：</p>
            <div class="model-info">
                <strong>实际使用的模型：</strong> <code>doubao-seed-1-6-250615</code><br>
                <strong>数据库中的模型：</strong> <code>loomrun_1.2ds</code> (DeepSeek, 2积分) 和 <code>loomrun_1.6db</code> (豆包, 4积分)<br>
                <strong>问题：</strong> <span class="warning">缺少模型名称映射逻辑</span>
            </div>
        </div>

        <div class="test-section">
            <h3>🔧 解决方案</h3>
            <p>已实现模型映射逻辑，将实际API模型名称映射到数据库配置：</p>
            
            <div class="step">
                <strong>1. 直接映射：</strong>
                <div class="code">doubao-seed-1-6-250615 → loomrun_1.6db</div>
            </div>
            
            <div class="step">
                <strong>2. 模糊匹配：</strong>
                <div class="code">
                    包含 "doubao" 或 "ep-" → loomrun_1.6db<br>
                    包含 "deepseek" → loomrun_1.2ds
                </div>
            </div>
            
            <div class="step">
                <strong>3. 预期结果：</strong>
                <div class="expected">
                    doubao-seed-1-6-250615 应该消耗 4积分<br>
                    deepseek-chat 应该消耗 2积分
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>🔍 调试信息</h3>
            <p>现在AI请求应该显示以下调试日志：</p>
            <div class="code">
🔍 [DEBUG] 开始查询模型积分配置: doubao-seed-1-6-250615<br>
🔄 模型映射: doubao-seed-1-6-250615 → loomrun_1.6db<br>
🔍 [DEBUG] 查询模型积分配置: 原始=doubao-seed-1-6-250615, 映射=loomrun_1.6db<br>
🔍 [DEBUG] 数据库中的所有AI模型: [...]<br>
✅ [DEBUG] 找到模型积分配置: loomrun_1.6db = 4积分<br>
✅ 积分检查通过: { pointsCost: 4, currentPoints: xxx }
            </div>
        </div>

        <div class="test-section">
            <h3>📊 测试步骤</h3>
            <ol>
                <li>重启开发服务器以加载新的映射逻辑</li>
                <li>发起一个AI请求（使用豆包模型）</li>
                <li>检查控制台日志中的调试信息</li>
                <li>确认积分是否正确扣除</li>
            </ol>
        </div>

        <div class="test-section">
            <h3>🎯 验证要点</h3>
            <ul>
                <li><strong>映射正确：</strong> doubao-seed-1-6-250615 → loomrun_1.6db</li>
                <li><strong>积分查询：</strong> loomrun_1.6db 应该返回 4积分</li>
                <li><strong>积分扣除：</strong> 成功完成AI请求后应该扣除4积分</li>
                <li><strong>错误处理：</strong> 模型繁忙等系统错误不应扣除积分</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>🛠️ 如果仍然不工作</h3>
            <p>检查以下几点：</p>
            <ul>
                <li>确保数据库中 <code>ai_models</code> 表有正确的记录</li>
                <li>确保 <code>is_active = 1</code></li>
                <li>检查数据库连接是否正常</li>
                <li>查看完整的调试日志</li>
            </ul>
        </div>
    </div>

    <script>
        console.log('🔍 积分消耗测试页面已加载');
        console.log('请在LoomRun系统中发起AI请求来测试积分消耗功能');
    </script>
</body>
</html>
