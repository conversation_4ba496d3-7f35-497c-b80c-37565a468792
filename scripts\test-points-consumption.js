const mysql = require('mysql2/promise');

async function testPointsConsumption() {
  let connection;
  
  try {
    // 创建数据库连接
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || 'root',
      database: process.env.DB_NAME || 'loomrun',
      charset: 'utf8mb4'
    });

    console.log('🔗 数据库连接成功');

    // 1. 检查AI模型配置
    console.log('\n🤖 检查AI模型配置...');
    const [aiModels] = await connection.execute(
      'SELECT model_key, model_name, points_per_request, is_active FROM ai_models ORDER BY display_order'
    );
    
    console.log('AI模型配置:');
    aiModels.forEach(model => {
      console.log(`  - ${model.model_key}: ${model.model_name} (${model.points_per_request}积分) ${model.is_active ? '✅' : '❌'}`);
    });

    // 2. 检查用户积分余额
    console.log('\n👥 检查用户积分余额...');
    const [users] = await connection.execute(
      'SELECT id, nickname, points, total_earned_points, total_spent_points FROM users ORDER BY id LIMIT 5'
    );
    
    console.log('用户积分状态:');
    users.forEach(user => {
      console.log(`  - 用户${user.id} (${user.nickname || '未设置'}): 当前${user.points}积分, 总获得${user.total_earned_points}, 总消费${user.total_spent_points}`);
    });

    // 3. 检查积分余额详情
    if (users.length > 0) {
      const testUserId = users[0].id;
      console.log(`\n💰 检查用户${testUserId}的积分余额详情...`);
      
      const [balances] = await connection.execute(
        `SELECT points_type, points_amount, expires_at, is_active, created_at 
         FROM user_points_balance 
         WHERE user_id = ? 
         ORDER BY created_at DESC`,
        [testUserId]
      );
      
      console.log('积分余额记录:');
      balances.forEach(balance => {
        const expiryInfo = balance.expires_at ? 
          `过期时间: ${balance.expires_at.toLocaleDateString()}` : 
          '永久有效';
        console.log(`  - ${balance.points_type}: ${balance.points_amount}积分 (${expiryInfo}) ${balance.is_active ? '✅' : '❌'}`);
      });
    }

    // 4. 检查积分交易记录
    console.log('\n📊 检查最近的积分交易记录...');
    const [transactions] = await connection.execute(
      `SELECT pt.user_id, pt.transaction_type, pt.points_amount, pt.source_type, 
              pt.balance_before, pt.balance_after, pt.created_at, u.nickname
       FROM points_transactions pt
       LEFT JOIN users u ON pt.user_id = u.id
       ORDER BY pt.created_at DESC
       LIMIT 10`
    );
    
    console.log('最近交易记录:');
    transactions.forEach(tx => {
      const typeIcon = tx.transaction_type === 'earn' ? '📈' : '📉';
      console.log(`  ${typeIcon} 用户${tx.user_id}(${tx.nickname || '未设置'}) ${tx.transaction_type === 'earn' ? '获得' : '消费'} ${tx.points_amount}积分`);
      console.log(`     来源: ${tx.source_type}, 余额: ${tx.balance_before} → ${tx.balance_after}, 时间: ${tx.created_at.toLocaleString()}`);
    });

    // 5. 检查积分消耗日志
    console.log('\n🔍 检查积分消耗日志...');
    const [consumptionLogs] = await connection.execute(
      `SELECT pcl.user_id, pcl.points_consumed, pcl.points_type, pcl.consumed_at,
              pt.source_type, pt.description, u.nickname
       FROM points_consumption_log pcl
       LEFT JOIN points_transactions pt ON pcl.transaction_id = pt.id
       LEFT JOIN users u ON pcl.user_id = u.id
       ORDER BY pcl.consumed_at DESC
       LIMIT 10`
    );
    
    if (consumptionLogs.length > 0) {
      console.log('消耗记录:');
      consumptionLogs.forEach(log => {
        console.log(`  🔥 用户${log.user_id}(${log.nickname || '未设置'}) 消耗 ${log.points_consumed}积分 (${log.points_type})`);
        console.log(`     来源: ${log.source_type}, 描述: ${log.description || '无'}, 时间: ${log.consumed_at.toLocaleString()}`);
      });
    } else {
      console.log('暂无消耗记录');
    }

    // 6. 检查聊天记录中的积分消耗
    console.log('\n💬 检查聊天记录中的积分消耗...');
    const [chatHistory] = await connection.execute(
      `SELECT ch.id, ch.user_id, ch.message_type, ch.model_used, ch.points_cost, 
              ch.created_at, u.nickname
       FROM chat_history ch
       LEFT JOIN users u ON ch.user_id = u.id
       WHERE ch.message_type = 'ai' AND ch.points_cost > 0
       ORDER BY ch.created_at DESC
       LIMIT 10`
    );
    
    if (chatHistory.length > 0) {
      console.log('AI对话积分消耗:');
      chatHistory.forEach(chat => {
        console.log(`  🤖 用户${chat.user_id}(${chat.nickname || '未设置'}) 使用 ${chat.model_used || '未知模型'} 消耗 ${chat.points_cost}积分`);
        console.log(`     时间: ${chat.created_at.toLocaleString()}`);
      });
    } else {
      console.log('暂无AI对话积分消耗记录');
    }

    // 7. 统计信息
    console.log('\n📈 积分系统统计...');
    
    // 总积分统计
    const [totalStats] = await connection.execute(
      `SELECT 
         SUM(points) as total_current_points,
         SUM(total_earned_points) as total_earned,
         SUM(total_spent_points) as total_spent,
         COUNT(*) as total_users
       FROM users`
    );
    
    const stats = totalStats[0];
    console.log(`总用户数: ${stats.total_users}`);
    console.log(`当前总积分: ${stats.total_current_points || 0}`);
    console.log(`历史总获得: ${stats.total_earned || 0}`);
    console.log(`历史总消费: ${stats.total_spent || 0}`);
    
    // 按类型统计积分余额
    const [balanceStats] = await connection.execute(
      `SELECT 
         points_type,
         SUM(points_amount) as total_amount,
         COUNT(*) as record_count
       FROM user_points_balance 
       WHERE is_active = 1 AND points_amount > 0
       GROUP BY points_type`
    );
    
    console.log('\n按类型统计积分余额:');
    balanceStats.forEach(stat => {
      console.log(`  ${stat.points_type}: ${stat.total_amount}积分 (${stat.record_count}条记录)`);
    });

    console.log('\n✅ 积分消耗系统检查完成');

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 数据库连接已关闭');
    }
  }
}

// 运行测试
testPointsConsumption().catch(console.error);
