import { NextRequest, NextResponse } from "next/server";
import { cookies } from "next/headers";
import { getUserByToken } from "@/lib/auth-service";
import { executeQuery, initDatabase } from "@/lib/mysql";

// 模型名称映射 - 将API模型名称转换为用户友好的显示名称
const MODEL_DISPLAY_MAPPING: Record<string, string> = {
  // DeepSeek 模型映射
  'deepseek-chat': 'LoomRun 1.2DS',
  'deepseek-coder': 'LoomRun 1.2DS',
  'deepseek-reasoner': 'LoomRun 1.2DS',

  // 豆包模型映射
  'doubao-lite-4k': 'LoomRun 1.6DB',
  'doubao-pro-4k': 'LoomRun 1.6DB',
  'doubao-pro-32k': 'LoomRun 1.6DB',
  'doubao-pro-128k': 'LoomRun 1.6DB',
  'doubao-seed-1-6-250615': 'LoomRun 1.6DB',
  'doubao-seed-1-6-250616': 'LoomRun 1.6DB',
  'doubao-seed-1-6-250617': 'LoomRun 1.6DB',

  // 其他可能的豆包模型变体
  'ep-20241230140251-8xqzx': 'LoomRun 1.6DB',
  'ep-20241230140251-xxxxx': 'LoomRun 1.6DB',

  // 数据库中的模型key直接映射
  'loomrun_1.2ds': 'LoomRun 1.2DS',
  'loomrun_1.6db': 'LoomRun 1.6DB',
};

// 获取用户友好的模型显示名称
const getDisplayModelName = (originalModelName: string): string => {
  // 1. 直接映射
  if (MODEL_DISPLAY_MAPPING[originalModelName]) {
    return MODEL_DISPLAY_MAPPING[originalModelName];
  }

  // 2. 模糊匹配 - 豆包模型
  if (originalModelName.includes('doubao') || originalModelName.includes('ep-')) {
    return 'LoomRun 1.6DB';
  }

  // 3. 模糊匹配 - DeepSeek模型
  if (originalModelName.includes('deepseek')) {
    return 'LoomRun 1.2DS';
  }

  // 4. 默认情况 - 返回原始名称
  return originalModelName;
};

// 初始化数据库
initDatabase().catch(console.error);

export async function GET(request: NextRequest) {
  try {
    const cookieStore = await cookies();
    const token = cookieStore.get("loomrun_token")?.value;

    if (!token) {
      return NextResponse.json({ error: "未登录" }, { status: 401 });
    }

    const user = await getUserByToken(token);

    if (!user) {
      return NextResponse.json({ error: "用户不存在" }, { status: 401 });
    }

    // 获取查询参数
    const { searchParams } = new URL(request.url);
    const limit = Math.min(parseInt(searchParams.get('limit') || '20', 10), 100); // 限制最大100条
    const offset = parseInt(searchParams.get('offset') || '0', 10);
    const transactionType = searchParams.get('type'); // 'earn' | 'spend' | null
    const pointsType = searchParams.get('points_type'); // 'activity' | 'subscription' | 'recharge' | null
    const sourceType = searchParams.get('source_type'); // 具体来源类型
    const dateFrom = searchParams.get('date_from'); // 开始日期
    const dateTo = searchParams.get('date_to'); // 结束日期

    // 构建查询条件（使用表别名避免歧义）
    let whereConditions = ['pt.user_id = ?'];
    let queryParams: (string | number)[] = [user.id];

    if (transactionType && ['earn', 'spend'].includes(transactionType)) {
      whereConditions.push('pt.transaction_type = ?');
      queryParams.push(transactionType);
    }

    if (pointsType && ['activity', 'subscription', 'recharge'].includes(pointsType)) {
      whereConditions.push('pt.points_type = ?');
      queryParams.push(pointsType);
    }

    if (sourceType) {
      whereConditions.push('pt.source_type = ?');
      queryParams.push(sourceType);
    }

    if (dateFrom) {
      whereConditions.push('pt.created_at >= ?');
      queryParams.push(dateFrom);
    }

    if (dateTo) {
      whereConditions.push('pt.created_at <= ?');
      queryParams.push(dateTo + ' 23:59:59');
    }

    const whereClause = whereConditions.join(' AND ');

    // 获取总数（用于分页）
    const countQuery = `SELECT COUNT(*) as total FROM points_transactions pt WHERE ${whereClause}`;
    const countResult = await executeQuery(countQuery, queryParams) as { total: number }[];
    const total = countResult[0]?.total || 0;

    // 获取详细记录
    const historyQuery = `
      SELECT
        pt.id,
        pt.user_id,
        pt.transaction_type,
        pt.points_amount,
        pt.balance_before,
        pt.balance_after,
        pt.source_type,
        pt.points_type,
        pt.expires_at,
        pt.balance_record_id,
        pt.source_id,
        pt.description,
        pt.metadata,
        pt.created_at,
        upb.expires_at as balance_expires_at,
        upb.is_active as balance_is_active
      FROM points_transactions pt
      LEFT JOIN user_points_balance upb ON pt.balance_record_id = upb.id
      WHERE ${whereClause}
      ORDER BY pt.created_at DESC, pt.id DESC
      LIMIT ? OFFSET ?
    `;

    // 创建新的参数数组，确保类型正确
    const finalQueryParams: (string | number)[] = [...queryParams, limit, offset];
    const history = await executeQuery(historyQuery, finalQueryParams);

    // 处理数据，添加额外信息
    const processedHistory = history.map((record: any) => {
      // 解析 metadata
      let metadata = null;
      if (record.metadata) {
        try {
          metadata = typeof record.metadata === 'string' ? JSON.parse(record.metadata) : record.metadata;
        } catch (e) {
          metadata = record.metadata;
        }
      }

      // 判断积分是否已过期
      const isExpired = record.expires_at ? new Date(record.expires_at) < new Date() : false;

      // 判断是否即将过期（7天内）
      const isExpiringSoon = record.expires_at ?
        (new Date(record.expires_at).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24) <= 7 &&
        (new Date(record.expires_at).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24) > 0 : false;

      // 处理描述中的模型名称显示
      let displayDescription = record.description;
      if (displayDescription && metadata?.model_key) {
        const displayModelName = getDisplayModelName(metadata.model_key);
        // 替换描述中的模型名称
        displayDescription = displayDescription.replace(
          /AI模型\s+[\w\-\.]+/g,
          `AI模型 ${displayModelName}`
        );
      }

      // 如果描述中包含原始模型名称，也进行替换
      if (displayDescription) {
        // 替换常见的模型名称模式
        displayDescription = displayDescription
          .replace(/doubao-seed-1-6-250615/g, 'LoomRun 1.6DB')
          .replace(/doubao-[\w\-]+/g, 'LoomRun 1.6DB')
          .replace(/deepseek-[\w\-]+/g, 'LoomRun 1.2DS')
          .replace(/ep-[\w\-]+/g, 'LoomRun 1.6DB');
      }

      return {
        ...record,
        metadata,
        isExpired,
        isExpiringSoon,
        // 格式化显示用的字段
        formattedAmount: record.transaction_type === 'earn' ? `+${record.points_amount}` : `-${record.points_amount}`,
        formattedDate: new Date(record.created_at).toLocaleString('zh-CN'),
        formattedExpiry: record.expires_at ? new Date(record.expires_at).toLocaleDateString('zh-CN') : null,
        // 添加用户友好的描述
        displayDescription: displayDescription || record.description,
        // 添加用户友好的模型名称（如果有的话）
        displayModelName: metadata?.model_key ? getDisplayModelName(metadata.model_key) : null
      };
    });

    return NextResponse.json({
      success: true,
      data: {
        history: processedHistory,
        pagination: {
          limit,
          offset,
          total,
          hasMore: offset + limit < total,
          currentPage: Math.floor(offset / limit) + 1,
          totalPages: Math.ceil(total / limit)
        },
        filters: {
          transactionType,
          pointsType,
          sourceType,
          dateFrom,
          dateTo
        }
      }
    });

  } catch (error) {
    console.error("获取积分历史失败:", error);
    return NextResponse.json({ error: "服务器错误" }, { status: 500 });
  }
}
