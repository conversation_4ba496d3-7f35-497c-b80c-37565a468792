import { executeQuery } from './mysql';
import { 
  getModelPointsCost, 
  checkUserPointsSufficient, 
  consumePointsWithPriority 
} from './points-service';

// AI请求错误类型定义
export interface AIRequestError {
  isModelBusy: boolean;
  isNetworkError: boolean;
  isQuotaExceeded: boolean;
  shouldChargePoints: boolean;
  errorMessage: string;
}

// 检测AI模型错误类型
export const detectAIError = (error: any): AIRequestError => {
  const errorMessage = error?.message || error?.toString() || 'Unknown error';
  
  // 模型繁忙相关错误（不扣积分）
  const busyPatterns = [
    'model is busy',
    'service is busy',
    'too many requests',
    'rate limit',
    '模型繁忙',
    '服务繁忙',
    'please try again later',
    'server is overloaded',
    'temporarily unavailable'
  ];

  // 网络连接错误（不扣积分）
  const networkPatterns = [
    'fetch failed',
    'timeout',
    'connect timeout',
    'network error',
    'connection refused',
    'connection reset',
    'ECONNREFUSED',
    'ETIMEDOUT',
    'ENOTFOUND'
  ];

  // 配额超限错误（不扣积分）
  const quotaPatterns = [
    'quota exceeded',
    'insufficient quota',
    'billing',
    'payment required',
    'account suspended',
    'api key invalid',
    'unauthorized'
  ];

  const isModelBusy = busyPatterns.some(pattern => 
    errorMessage.toLowerCase().includes(pattern.toLowerCase())
  );

  const isNetworkError = networkPatterns.some(pattern => 
    errorMessage.toLowerCase().includes(pattern.toLowerCase())
  );

  const isQuotaExceeded = quotaPatterns.some(pattern => 
    errorMessage.toLowerCase().includes(pattern.toLowerCase())
  );

  // 只有在非系统错误时才扣积分
  const shouldChargePoints = !isModelBusy && !isNetworkError && !isQuotaExceeded;

  return {
    isModelBusy,
    isNetworkError,
    isQuotaExceeded,
    shouldChargePoints,
    errorMessage
  };
};

// AI请求前的积分检查和预扣
export const preCheckAndReservePoints = async (
  userId: number,
  modelKey: string
): Promise<{ 
  canProceed: boolean; 
  pointsCost: number; 
  currentPoints: number;
  errorMessage?: string;
}> => {
  try {
    // 1. 获取模型积分消耗
    const pointsCost = await getModelPointsCost(modelKey);
    
    if (pointsCost === 0) {
      // 免费模型，直接允许
      return { 
        canProceed: true, 
        pointsCost: 0, 
        currentPoints: 0 
      };
    }

    // 2. 检查用户积分余额
    const currentPointsResult = await executeQuery(
      'SELECT points FROM users WHERE id = ?',
      [userId]
    ) as { points: number }[];

    const currentPoints = currentPointsResult[0]?.points || 0;
    const hasSufficientPoints = currentPoints >= pointsCost;

    if (!hasSufficientPoints) {
      return {
        canProceed: false,
        pointsCost,
        currentPoints,
        errorMessage: `积分不足，需要 ${pointsCost} 积分，当前余额 ${currentPoints} 积分`
      };
    }

    return {
      canProceed: true,
      pointsCost,
      currentPoints
    };

  } catch (error) {
    console.error('AI请求积分预检查失败:', error);
    return {
      canProceed: false,
      pointsCost: 0,
      currentPoints: 0,
      errorMessage: '积分检查失败，请稍后重试'
    };
  }
};

// AI请求成功后的积分扣除
export const chargePointsForAIRequest = async (
  userId: number,
  modelKey: string,
  chatHistoryId?: number,
  requestMetadata?: Record<string, unknown>
): Promise<{ success: boolean; transactionId?: number; pointsCharged: number }> => {
  try {
    // 1. 获取模型积分消耗
    const pointsCost = await getModelPointsCost(modelKey);
    
    if (pointsCost === 0) {
      // 免费模型，不扣积分但记录使用
      if (chatHistoryId) {
        await executeQuery(
          'UPDATE chat_history SET points_cost = 0, model_used = ? WHERE id = ?',
          [modelKey, chatHistoryId]
        );
      }
      return { success: true, pointsCharged: 0 };
    }

    // 2. 执行智能积分消耗
    const consumeResult = await consumePointsWithPriority(
      userId,
      pointsCost,
      'ai_request',
      chatHistoryId?.toString(),
      `AI模型 ${modelKey} 请求消耗`,
      {
        model_key: modelKey,
        chat_history_id: chatHistoryId,
        request_timestamp: new Date().toISOString(),
        ...requestMetadata
      }
    );

    if (!consumeResult.success) {
      console.error('AI请求积分扣除失败:', { userId, modelKey, pointsCost });
      return { success: false, pointsCharged: 0 };
    }

    // 3. 更新聊天记录的积分消耗信息
    if (chatHistoryId) {
      await executeQuery(
        'UPDATE chat_history SET points_cost = ?, model_used = ? WHERE id = ?',
        [pointsCost, modelKey, chatHistoryId]
      );
    }

    console.log(`✅ AI请求积分扣除成功: 用户${userId} 模型${modelKey} 消耗${pointsCost}积分`);
    
    return { 
      success: true, 
      transactionId: consumeResult.transactionId,
      pointsCharged: pointsCost 
    };

  } catch (error) {
    console.error('AI请求积分扣除失败:', error);
    return { success: false, pointsCharged: 0 };
  }
};

// AI请求失败时的积分退还（如果已扣除）
export const refundPointsForFailedAIRequest = async (
  userId: number,
  transactionId: number,
  reason: string
): Promise<boolean> => {
  try {
    // 获取原始交易信息
    const transactions = await executeQuery(
      'SELECT points_amount, source_type FROM points_transactions WHERE id = ? AND user_id = ?',
      [transactionId, userId]
    ) as { points_amount: number; source_type: string }[];

    if (transactions.length === 0) {
      console.warn('未找到需要退还的积分交易记录:', { userId, transactionId });
      return false;
    }

    const originalAmount = transactions[0].points_amount;

    // 创建退还交易记录
    const refundResult = await consumePointsWithPriority(
      userId,
      -originalAmount, // 负数表示退还
      'refund',
      transactionId.toString(),
      `AI请求失败积分退还: ${reason}`,
      {
        original_transaction_id: transactionId,
        refund_reason: reason,
        refund_timestamp: new Date().toISOString()
      }
    );

    console.log(`✅ AI请求失败积分退还: 用户${userId} 退还${originalAmount}积分`);
    return refundResult.success;

  } catch (error) {
    console.error('AI请求积分退还失败:', error);
    return false;
  }
};

// 获取用户AI使用统计
export const getUserAIUsageStats = async (userId: number, days: number = 30) => {
  try {
    const stats = await executeQuery(
      `SELECT 
        COUNT(*) as total_requests,
        SUM(points_cost) as total_points_spent,
        COUNT(DISTINCT model_used) as models_used,
        model_used,
        COUNT(*) as model_requests,
        SUM(points_cost) as model_points
       FROM chat_history 
       WHERE user_id = ? 
         AND message_type = 'ai' 
         AND created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
       GROUP BY model_used
       ORDER BY model_points DESC`,
      [userId, days]
    );

    return stats;
  } catch (error) {
    console.error('获取AI使用统计失败:', error);
    return [];
  }
};
