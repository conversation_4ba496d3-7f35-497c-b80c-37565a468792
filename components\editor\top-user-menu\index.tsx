"use client";
import { useState, useRef, useEffect } from "react";
import { useUser } from "@/loomrunhooks/useUser";
import { cn } from "@/lib/utils";
import { User } from "@/types";
import { LogIn, UserCircle, Coins } from "lucide-react";
import { useRouter } from "next/navigation";

// 导入弹窗组件
import SettingsModal from "@/components/settings-modal";
import { AuthModal } from "@/components/auth-modal";
import ProfileModal from "@/components/profile-modal";
import { ThemeToggleThreeState } from "@/components/ui/theme-toggle-three-state";

interface TopUserMenuProps {
  className?: string;
  isChatOpen?: boolean;
  setIsChatOpen?: (isOpen: boolean) => void;
}

// 获取用户显示名称
const getUserDisplayName = (user: User) => {
  if (user.nickname) return user.nickname;
  if (user.phone) {
    // 手机用户显示手机号
    return user.phone;
  }
  return user.name || '用户';
};

export function TopUserMenu({ 
  className,
  isChatOpen: externalChatOpen,
  setIsChatOpen: setExternalChatOpen
}: TopUserMenuProps) {
  const { user, logout } = useUser();
  const router = useRouter();
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);
  const [isProfileOpen, setIsProfileOpen] = useState(false);
  const [authModalOpen, setAuthModalOpen] = useState(false);

  // 积分余额状态
  const [pointsBalance, setPointsBalance] = useState<{
    subscription: number;
    activity: number;
    recharge: number;
  }>({
    subscription: 0,
    activity: 0,
    recharge: 0
  });
  
  // 使用外部传入的聊天状态，如果没有传入则使用内部状态
  const [internalChatOpen, setInternalChatOpen] = useState(false);
  const isChatOpen = externalChatOpen !== undefined ? externalChatOpen : internalChatOpen;
  const setIsChatOpen = setExternalChatOpen || setInternalChatOpen;
  const userMenuRef = useRef<HTMLDivElement>(null);

  // 获取积分余额
  const fetchPointsBalance = async () => {
    if (!user) return;

    try {
      const response = await fetch('/api/points/balance');
      const data = await response.json();

      if (data.success && data.data.summary) {
        const summary = data.data.summary;
        const balance = {
          subscription: 0,
          activity: 0,
          recharge: 0
        };

        // 按积分类型统计余额
        summary.forEach((item: any) => {
          if (item.points_type === 'subscription') {
            balance.subscription = item.total_points || 0;
          } else if (item.points_type === 'activity') {
            balance.activity = item.total_points || 0;
          } else if (item.points_type === 'recharge') {
            balance.recharge = item.total_points || 0;
          }
        });

        setPointsBalance(balance);
      }
    } catch (error) {
      console.error('获取积分余额失败:', error);
    }
  };

  // 当用户登录时获取积分余额
  useEffect(() => {
    if (user && isUserMenuOpen) {
      fetchPointsBalance();
    }
  }, [user, isUserMenuOpen]);

  // 处理点击外部关闭菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (userMenuRef.current && !userMenuRef.current.contains(event.target as Node)) {
        setIsUserMenuOpen(false);
      }
    };

    if (isUserMenuOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [isUserMenuOpen]);

  // 登出处理
  const handleLogout = async () => {
    try {
      await logout();
      setIsUserMenuOpen(false);
    } catch (error) {
      console.error('登出失败:', error);
    }
  };

  // 登录成功处理
  const handleLoginSuccess = async () => {
    // AuthModal 已经处理了用户状态刷新，这里只需要关闭弹窗
    setAuthModalOpen(false);
    console.log('✅ 登录成功，用户菜单已更新');
  };

  // 如果用户未登录，显示登录按钮
  if (!user) {
    return (
      <>
        <button
          onClick={() => setAuthModalOpen(true)}
          className={cn(
            "flex items-center gap-1.5 px-2 py-1 rounded-lg transition-all duration-200",
            "bg-secondary/80 hover:bg-secondary border border-border hover:border-muted-foreground",
            "text-muted-foreground hover:text-foreground text-sm font-medium",
            "shadow-sm hover:shadow-md",
            className
          )}
        >
                     <LogIn className="w-5 h-5" />
           <span>登录</span>
        </button>

        {/* 登录弹窗 */}
        <AuthModal
          open={authModalOpen}
          onClose={() => setAuthModalOpen(false)}
          onSuccess={handleLoginSuccess}
        />
      </>
    );
  }

  return (
    <>
      <div className={cn("relative", className)} ref={userMenuRef}>
        {/* 用户头像按钮 */}
        <button 
          className="flex items-center gap-1.5 hover:bg-secondary/80 rounded-lg p-0.5 transition-all duration-200 cursor-pointer"
          onClick={(e) => {
            e.stopPropagation();
            setIsUserMenuOpen(!isUserMenuOpen);
          }}
        >
          {/* 用户头像 */}
          <div className="relative">
            {user.avatar_url ? (
              // 微信用户显示微信头像
              <img 
                src={user.avatar_url}
                alt="用户头像"
                                 className="w-9 h-9 rounded-full object-cover shadow-lg border border-transparent dark:border-transparent border-gray-300"
                onError={(e) => {
                  // 头像加载失败时显示默认头像
                  const target = e.target as HTMLImageElement;
                  target.style.display = 'none';
                  target.nextElementSibling?.classList.remove('hidden');
                }}
              />
            ) : null}
                         <div className={`w-9 h-9 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white font-semibold text-sm shadow-lg border border-transparent dark:border-transparent border-gray-300 ${user.avatar_url ? 'hidden' : ''}`}>
              {getUserDisplayName(user).charAt(0).toUpperCase()}
            </div>
            {/* 在线状态指示器 */}
                         <div className="absolute -bottom-0.5 -right-0.5 w-3.5 h-3.5 bg-green-500 rounded-full border border-background">
              <div className="w-full h-full bg-green-400 rounded-full animate-pulse" />
            </div>
          </div>
          
          {/* Pro 标识（如果有） */}
          {user.isPro && (
            <span className="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-amber-500/20 text-amber-400 border border-amber-500/30">
                             <svg className="w-3.5 h-3.5 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
              </svg>
              Pro
            </span>
          )}
        </button>

        {/* 弹出菜单卡片 - V0.dev 风格 */}
        <div className={cn(
          "absolute top-full right-0 mt-2 w-64 bg-white dark:bg-black border border-border rounded-xl shadow-xl overflow-hidden z-50 transition-all duration-200",
          {
            "opacity-100 visible transform translate-y-0": isUserMenuOpen,
            "opacity-0 invisible transform translate-y-2": !isUserMenuOpen,
          }
        )}>
          {/* 用户信息头部 */}
          <div className="p-4 border-b border-border">
            <div className="flex items-center gap-3">
              {/* 用户头像 */}
              <div className="relative">
                {user.avatar_url ? (
                  <img 
                    src={user.avatar_url}
                    alt="用户头像"
                    className="w-12 h-12 rounded-full object-cover shadow-sm ring-2 ring-border border border-transparent dark:border-transparent border-gray-200"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.style.display = 'none';
                      target.nextElementSibling?.classList.remove('hidden');
                    }}
                  />
                ) : null}
                <div className={`w-12 h-12 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white font-semibold text-lg shadow-sm ring-2 ring-border border border-transparent dark:border-transparent border-gray-200 ${user.avatar_url ? 'hidden' : ''}`}>
                  {getUserDisplayName(user).charAt(0).toUpperCase()}
                </div>
                {/* 在线状态 */}
                <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-background" />
              </div>
              
              {/* 用户信息 */}
              <div className="flex-1 min-w-0">
                <div className="font-semibold text-foreground truncate text-base">
                  {getUserDisplayName(user)}
                </div>
                <div className="text-sm text-muted-foreground truncate">
                  {user.phone || user.wechat_openid ? '已登录' : '本地用户'}
                </div>
                {user.isPro && (
                  <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-amber-100 dark:bg-amber-900/20 text-amber-800 dark:text-amber-400 border border-amber-200 dark:border-amber-800 mt-1">
                    <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                    Pro
                  </span>
                )}
              </div>
            </div>
          </div>



          <div className="border-t border-border" />

          {/* 菜单项 */}
          <div className="p-2 space-y-1">
            {/* 个人中心 */}
            <button
              className="w-full flex items-center gap-3 px-3 py-2.5 text-sm text-foreground hover:text-foreground hover:bg-accent rounded-lg transition-all duration-200 group"
              onClick={() => {
                setIsProfileOpen(true);
                setIsUserMenuOpen(false);
              }}
            >
              <UserCircle className="w-5 h-5 text-muted-foreground" />
              <span className="font-medium">个人设置</span>
            </button>

            {/* 系统设置 */}
            <button
              className="w-full flex items-center gap-3 px-3 py-2.5 text-sm text-foreground hover:text-foreground hover:bg-accent rounded-lg transition-all duration-200 group"
              onClick={() => {
                setIsSettingsOpen(true);
                setIsUserMenuOpen(false);
              }}
            >
              <svg className="w-5 h-5 text-muted-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1 1 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
              <span className="font-medium">系统设置</span>
            </button>



          </div>

          <div className="border-t border-border" />

          {/* 积分显示卡片 */}
          <div className="p-3">
            <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/30 dark:to-purple-950/30 rounded-lg p-4 border border-blue-100 dark:border-blue-800/30">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2">
                  <div className="w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
                    <Coins className="w-4 h-4 text-white" />
                  </div>
                  <span className="font-semibold text-foreground">我的积分</span>
                </div>
                <button
                  className="text-xs text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-medium"
                  onClick={() => {
                    setIsUserMenuOpen(false);
                    router.push('/points-pro');
                  }}
                >
                  详情
                </button>
              </div>

              <div className="flex items-baseline gap-1 mb-2">
                <span className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                  {user.points?.toLocaleString() || 0}
                </span>
                <span className="text-sm text-muted-foreground">积分</span>
              </div>

              <div className="grid grid-cols-3 gap-2 text-xs">
                <div className="text-center p-2 bg-blue-50 dark:bg-blue-950/30 rounded-md">
                  <div className="font-semibold text-blue-600 dark:text-blue-400">
                    {pointsBalance.subscription.toLocaleString()}
                  </div>
                  <div className="text-muted-foreground">订阅</div>
                </div>
                <div className="text-center p-2 bg-green-50 dark:bg-green-950/30 rounded-md">
                  <div className="font-semibold text-green-600 dark:text-green-400">
                    {pointsBalance.activity.toLocaleString()}
                  </div>
                  <div className="text-muted-foreground">活动</div>
                </div>
                <div className="text-center p-2 bg-purple-50 dark:bg-purple-950/30 rounded-md">
                  <div className="font-semibold text-purple-600 dark:text-purple-400">
                    {pointsBalance.recharge.toLocaleString()}
                  </div>
                  <div className="text-muted-foreground">充值</div>
                </div>
              </div>
            </div>
          </div>

          <div className="border-t border-border" />

          {/* 产品与帮助区域 */}
          <div className="p-2">
            <div className="text-xs font-medium text-muted-foreground uppercase tracking-wider mb-3 px-1">
              产品与帮助
            </div>

            <button
              className="w-full flex items-center gap-3 px-3 py-2.5 text-sm text-foreground hover:text-foreground hover:bg-accent rounded-lg transition-all duration-200 group"
              onClick={() => {
                window.open('http://localhost:3141/features', '_blank');
                setIsUserMenuOpen(false);
              }}
            >
              <svg className="w-5 h-5 text-muted-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span className="font-medium">功能介绍</span>
            </button>

            <button
              className="w-full flex items-center gap-3 px-3 py-2.5 text-sm text-foreground hover:text-foreground hover:bg-accent rounded-lg transition-all duration-200 group"
              onClick={() => {
                // 使用指南 - 待实现
                console.log('使用指南');
              }}
            >
              <svg className="w-5 h-5 text-muted-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
              </svg>
              <span className="font-medium">使用指南</span>
            </button>

            <button
              className="w-full flex items-center gap-3 px-3 py-2.5 text-sm text-foreground hover:text-foreground hover:bg-accent rounded-lg transition-all duration-200 group"
              onClick={() => {
                // 常见问题 - 待实现
                console.log('常见问题');
              }}
            >
              <svg className="w-5 h-5 text-muted-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span className="font-medium">常见问题</span>
            </button>

            {/* 联系我们 */}
            <button
              className="w-full flex items-center gap-3 px-3 py-2.5 text-sm text-foreground hover:text-foreground hover:bg-accent rounded-lg transition-all duration-200 group"
              onClick={() => {
                setIsChatOpen(true);
                setIsUserMenuOpen(false);
              }}
            >
              <svg className="w-5 h-5 text-muted-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
              <span className="font-medium">联系我们</span>
            </button>

            {/* 主题切换 */}
            <div className="flex items-center justify-between px-3 py-2.5 mt-3">
              <span className="font-medium text-foreground">主题</span>
              <ThemeToggleThreeState className="h-7" />
            </div>

          </div>

          <div className="border-t border-zinc-200 dark:border-zinc-700" />

          {/* 退出登录 */}
          <div className="p-2">
            <button
              className="w-full flex items-center gap-3 px-3 py-2.5 text-sm text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-all duration-200 group"
              onClick={handleLogout}
            >
              <svg className="w-5 h-5 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
              </svg>
              <span className="font-medium">退出登录</span>
            </button>
          </div>
        </div>
      </div>

      {/* 个人中心弹窗 */}
      <ProfileModal 
        isOpen={isProfileOpen} 
        onClose={() => setIsProfileOpen(false)} 
      />

      {/* 设置弹窗 */}
      <SettingsModal 
        isOpen={isSettingsOpen} 
        onClose={() => setIsSettingsOpen(false)} 
      />

    </>
  );
} 