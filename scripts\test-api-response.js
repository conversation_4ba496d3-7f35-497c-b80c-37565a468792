// 测试积分历史API的实际响应
const fetch = require('node-fetch');

async function testAPIResponse() {
  try {
    console.log('🔍 测试积分历史API响应...');
    
    // 注意：这需要有效的登录token，这里只是演示结构
    console.log('⚠️ 需要在浏览器中手动测试API响应');
    console.log('');
    console.log('📋 测试步骤:');
    console.log('1. 在浏览器中打开开发者工具');
    console.log('2. 访问积分页面');
    console.log('3. 在Network标签中找到 /api/points/history 请求');
    console.log('4. 查看响应数据的结构');
    console.log('');
    console.log('🔍 需要检查的关键点:');
    console.log('- description字段的实际内容');
    console.log('- metadata字段是否存在且包含model_key');
    console.log('- displayDescription字段是否正确生成');
    console.log('');
    
    // 模拟API响应处理逻辑
    console.log('🧪 模拟API处理逻辑测试:');
    
    const mockRecord = {
      id: 1,
      description: 'AI模型 doubao-seed-1-6-250615 请求消耗',
      metadata: JSON.stringify({
        model_key: 'doubao-seed-1-6-250615',
        request_type: 'website_generation'
      }),
      source_type: 'ai_request'
    };
    
    console.log('原始记录:', mockRecord);
    
    // 模拟处理逻辑
    let metadata = null;
    if (mockRecord.metadata) {
      try {
        metadata = JSON.parse(mockRecord.metadata);
      } catch (e) {
        metadata = mockRecord.metadata;
      }
    }
    
    console.log('解析后的metadata:', metadata);
    
    // 模型映射
    const MODEL_DISPLAY_MAPPING = {
      'doubao-seed-1-6-250615': 'LoomRun 1.6DB',
      'deepseek-chat': 'LoomRun 1.2DS',
    };
    
    const getDisplayModelName = (originalModelName) => {
      if (MODEL_DISPLAY_MAPPING[originalModelName]) {
        return MODEL_DISPLAY_MAPPING[originalModelName];
      }
      
      if (originalModelName.includes('doubao') || originalModelName.includes('ep-')) {
        return 'LoomRun 1.6DB';
      }
      
      if (originalModelName.includes('deepseek')) {
        return 'LoomRun 1.2DS';
      }
      
      return originalModelName;
    };
    
    // 处理描述
    let displayDescription = mockRecord.description;
    if (displayDescription && metadata?.model_key) {
      const displayModelName = getDisplayModelName(metadata.model_key);
      console.log(`模型映射: ${metadata.model_key} → ${displayModelName}`);
      
      // 替换描述中的模型名称
      displayDescription = displayDescription.replace(
        /AI模型\s+[\w\-\.]+/g,
        `AI模型 ${displayModelName}`
      );
    }
    
    // 如果描述中包含原始模型名称，也进行替换
    if (displayDescription) {
      displayDescription = displayDescription
        .replace(/doubao-seed-1-6-250615/g, 'LoomRun 1.6DB')
        .replace(/doubao-[\w\-]+/g, 'LoomRun 1.6DB')
        .replace(/deepseek-[\w\-]+/g, 'LoomRun 1.2DS')
        .replace(/ep-[\w\-]+/g, 'LoomRun 1.6DB');
    }
    
    console.log('处理后的描述:', displayDescription);
    
    const processedRecord = {
      ...mockRecord,
      metadata,
      displayDescription: displayDescription || mockRecord.description,
      displayModelName: metadata?.model_key ? getDisplayModelName(metadata.model_key) : null
    };
    
    console.log('最终处理结果:', processedRecord);
    
    console.log('\n✅ 模拟处理完成');
    console.log('如果实际API没有生效，可能的原因:');
    console.log('1. 数据库中的description字段格式不匹配');
    console.log('2. metadata字段为空或格式不正确');
    console.log('3. 前端没有使用displayDescription字段');
    console.log('4. API缓存问题');
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

testAPIResponse();
