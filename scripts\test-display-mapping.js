// 测试模型名称显示映射
const fetch = require('node-fetch');

// 模型显示名称映射（与API保持一致）
const MODEL_DISPLAY_MAPPING = {
  // DeepSeek 模型映射
  'deepseek-chat': 'LoomRun 1.2DS',
  'deepseek-coder': 'LoomRun 1.2DS',
  'deepseek-reasoner': 'LoomRun 1.2DS',
  
  // 豆包模型映射
  'doubao-lite-4k': 'LoomRun 1.6DB',
  'doubao-pro-4k': 'LoomRun 1.6DB',
  'doubao-pro-32k': 'LoomRun 1.6DB',
  'doubao-pro-128k': 'LoomRun 1.6DB',
  'doubao-seed-1-6-250615': 'LoomRun 1.6DB',
  'doubao-seed-1-6-250616': 'LoomRun 1.6DB',
  'doubao-seed-1-6-250617': 'LoomRun 1.6DB',
  
  // 其他可能的豆包模型变体
  'ep-20241230140251-8xqzx': 'LoomRun 1.6DB',
  'ep-20241230140251-xxxxx': 'LoomRun 1.6DB',
  
  // 数据库中的模型key直接映射
  'loomrun_1.2ds': 'LoomRun 1.2DS',
  'loomrun_1.6db': 'LoomRun 1.6DB',
};

// 获取用户友好的模型显示名称
const getDisplayModelName = (originalModelName) => {
  // 1. 直接映射
  if (MODEL_DISPLAY_MAPPING[originalModelName]) {
    return MODEL_DISPLAY_MAPPING[originalModelName];
  }
  
  // 2. 模糊匹配 - 豆包模型
  if (originalModelName.includes('doubao') || originalModelName.includes('ep-')) {
    return 'LoomRun 1.6DB';
  }
  
  // 3. 模糊匹配 - DeepSeek模型
  if (originalModelName.includes('deepseek')) {
    return 'LoomRun 1.2DS';
  }
  
  // 4. 默认情况 - 返回原始名称
  return originalModelName;
};

function testDisplayMapping() {
  console.log('🔍 测试模型显示名称映射:\n');
  
  const testCases = [
    {
      input: 'doubao-seed-1-6-250615',
      expected: 'LoomRun 1.6DB',
      description: '当前使用的豆包模型'
    },
    {
      input: 'deepseek-chat',
      expected: 'LoomRun 1.2DS',
      description: 'DeepSeek基础模型'
    },
    {
      input: 'doubao-pro-4k',
      expected: 'LoomRun 1.6DB',
      description: '豆包专业版'
    },
    {
      input: 'ep-20241230140251-8xqzx',
      expected: 'LoomRun 1.6DB',
      description: '豆包endpoint模型'
    },
    {
      input: 'loomrun_1.2ds',
      expected: 'LoomRun 1.2DS',
      description: '数据库中的DeepSeek模型key'
    },
    {
      input: 'loomrun_1.6db',
      expected: 'LoomRun 1.6DB',
      description: '数据库中的豆包模型key'
    }
  ];

  let passedTests = 0;
  let totalTests = testCases.length;

  testCases.forEach((testCase, index) => {
    console.log(`📋 测试 ${index + 1}: ${testCase.description}`);
    console.log(`   输入: ${testCase.input}`);
    
    const result = getDisplayModelName(testCase.input);
    const passed = result === testCase.expected;
    
    console.log(`   输出: ${result}`);
    console.log(`   期望: ${testCase.expected}`);
    console.log(`   结果: ${passed ? '✅ 通过' : '❌ 失败'}\n`);
    
    if (passed) passedTests++;
  });

  console.log(`📊 测试结果: ${passedTests}/${totalTests} 通过`);
  
  if (passedTests === totalTests) {
    console.log('🎉 所有测试通过！显示映射逻辑正确');
  } else {
    console.log('⚠️ 部分测试失败，需要检查映射逻辑');
  }

  // 测试描述替换逻辑
  console.log('\n🔍 测试描述替换逻辑:');
  
  const descriptionTests = [
    {
      input: 'AI模型 doubao-seed-1-6-250615 请求消耗',
      expected: 'AI模型 LoomRun 1.6DB 请求消耗',
      description: '标准AI模型描述'
    },
    {
      input: '使用deepseek-chat模型生成内容',
      expected: '使用LoomRun 1.2DS模型生成内容',
      description: 'DeepSeek模型描述'
    }
  ];

  descriptionTests.forEach((test, index) => {
    console.log(`📝 描述测试 ${index + 1}: ${test.description}`);
    console.log(`   原始: ${test.input}`);
    
    // 模拟描述替换逻辑
    let result = test.input
      .replace(/AI模型\s+[\w\-\.]+/g, (match) => {
        const modelName = match.replace('AI模型 ', '');
        return `AI模型 ${getDisplayModelName(modelName)}`;
      })
      .replace(/doubao-seed-1-6-250615/g, 'LoomRun 1.6DB')
      .replace(/doubao-[\w\-]+/g, 'LoomRun 1.6DB')
      .replace(/deepseek-[\w\-]+/g, 'LoomRun 1.2DS')
      .replace(/ep-[\w\-]+/g, 'LoomRun 1.6DB');
    
    const passed = result === test.expected;
    
    console.log(`   结果: ${result}`);
    console.log(`   期望: ${test.expected}`);
    console.log(`   状态: ${passed ? '✅ 通过' : '❌ 失败'}\n`);
  });

  console.log('✅ 显示映射测试完成');
  console.log('\n📋 实施说明:');
  console.log('1. API已更新，会自动转换模型名称');
  console.log('2. 前端页面已更新，使用displayDescription字段');
  console.log('3. 用户将看到友好的LoomRun模型名称而不是原始API名称');
}

// 运行测试
testDisplayMapping();
