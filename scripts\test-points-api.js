// 测试积分消耗API
const fetch = require('node-fetch');

async function testPointsAPI() {
  const baseURL = 'http://localhost:3000';
  
  try {
    console.log('🔍 测试积分消耗API...\n');

    // 1. 测试模型积分配置查询
    console.log('📋 测试模型积分配置查询:');
    
    const testModels = [
      'doubao-seed-1-6-250615',
      'deepseek-chat',
      'loomrun_1.6db',
      'loomrun_1.2ds'
    ];

    // 模拟getModelPointsCost函数的逻辑
    const MODEL_MAPPING = {
      'deepseek-chat': 'loomrun_1.2ds',
      'doubao-seed-1-6-250615': 'loomrun_1.6db',
    };

    const getMappedModelKey = (originalModelKey) => {
      if (MODEL_MAPPING[originalModelKey]) {
        return MODEL_MAPPING[originalModelKey];
      }
      
      if (originalModelKey.includes('doubao') || originalModelKey.includes('ep-')) {
        return 'loomrun_1.6db';
      }
      
      if (originalModelKey.includes('deepseek')) {
        return 'loomrun_1.2ds';
      }
      
      return originalModelKey;
    };

    testModels.forEach(model => {
      const mapped = getMappedModelKey(model);
      console.log(`  ${model} → ${mapped}`);
    });

    // 2. 测试实际的AI请求（需要登录token）
    console.log('\n🤖 模拟AI请求测试:');
    console.log('注意: 需要有效的登录token才能测试实际的积分消耗');
    
    // 模拟请求数据
    const testRequest = {
      prompt: '测试积分消耗',
      provider: 'doubao-official',
      model: 'doubao-seed-1-6-250615',
      images: []
    };

    console.log('请求数据:', JSON.stringify(testRequest, null, 2));
    console.log('预期映射: doubao-seed-1-6-250615 → loomrun_1.6db');
    console.log('预期积分消耗: 4积分 (根据数据库配置)');

    // 3. 检查映射逻辑的关键点
    console.log('\n🔍 映射逻辑验证:');
    
    const criticalTests = [
      {
        input: 'doubao-seed-1-6-250615',
        expectedMapping: 'loomrun_1.6db',
        expectedPoints: 4,
        description: '当前日志中的豆包模型'
      },
      {
        input: 'deepseek-chat',
        expectedMapping: 'loomrun_1.2ds', 
        expectedPoints: 2,
        description: 'DeepSeek模型'
      }
    ];

    criticalTests.forEach(test => {
      const mapped = getMappedModelKey(test.input);
      const mappingCorrect = mapped === test.expectedMapping;
      
      console.log(`\n📋 ${test.description}:`);
      console.log(`  输入模型: ${test.input}`);
      console.log(`  映射结果: ${mapped}`);
      console.log(`  映射正确: ${mappingCorrect ? '✅' : '❌'}`);
      console.log(`  预期积分: ${test.expectedPoints}`);
    });

    // 4. 提供调试建议
    console.log('\n🛠️ 调试建议:');
    console.log('1. 确保数据库中有以下记录:');
    console.log('   - loomrun_1.2ds: 2积分 (DeepSeek)');
    console.log('   - loomrun_1.6db: 4积分 (豆包)');
    console.log('');
    console.log('2. 检查AI请求日志中的映射信息:');
    console.log('   - 应该看到: "🔄 模型映射: doubao-seed-1-6-250615 → loomrun_1.6db"');
    console.log('   - 应该看到: "✅ 找到模型积分配置: loomrun_1.6db = 4积分"');
    console.log('');
    console.log('3. 如果仍然显示0积分，检查:');
    console.log('   - ai_models表中的is_active字段是否为1');
    console.log('   - 数据库连接是否正常');
    console.log('   - 映射逻辑是否正确执行');

    console.log('\n✅ 测试完成');

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  }
}

// 运行测试
testPointsAPI().catch(console.error);
