// 快速测试积分消耗系统
const mysql = require('mysql2/promise');

async function quickTest() {
  let connection;
  
  try {
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root', 
      password: process.env.DB_PASSWORD || 'root',
      database: process.env.DB_NAME || 'loomrun',
      charset: 'utf8mb4'
    });

    console.log('🔗 连接数据库成功');

    // 1. 检查AI模型表是否存在数据
    try {
      const [models] = await connection.execute('SELECT COUNT(*) as count FROM ai_models');
      console.log(`📊 AI模型数量: ${models[0].count}`);
      
      if (models[0].count === 0) {
        console.log('⚠️ 没有AI模型配置，插入默认数据...');
        await connection.execute(`
          INSERT INTO ai_models (model_key, model_name, points_per_request, description, display_order) VALUES
          ('deepseek-chat', 'DeepSeek Chat', 2, 'DeepSeek基础对话模型', 1),
          ('doubao-pro-4k', 'Doubao Pro 4K', 3, '豆包专业级模型', 2)
        `);
        console.log('✅ 默认AI模型配置已插入');
      }
    } catch (error) {
      console.error('❌ AI模型表检查失败:', error.message);
    }

    // 2. 检查用户表
    try {
      const [users] = await connection.execute('SELECT COUNT(*) as count FROM users');
      console.log(`👥 用户数量: ${users[0].count}`);
    } catch (error) {
      console.error('❌ 用户表检查失败:', error.message);
    }

    // 3. 检查积分相关表
    const tables = [
      'points_transactions',
      'user_points_balance', 
      'points_consumption_log',
      'points_expiry_log'
    ];

    for (const table of tables) {
      try {
        const [result] = await connection.execute(`SELECT COUNT(*) as count FROM ${table}`);
        console.log(`📋 ${table}: ${result[0].count} 条记录`);
      } catch (error) {
        console.error(`❌ ${table} 表检查失败:`, error.message);
      }
    }

    // 4. 测试模型积分查询
    try {
      const [modelPoints] = await connection.execute(
        'SELECT model_key, points_per_request FROM ai_models WHERE model_key = ?',
        ['deepseek-chat']
      );
      
      if (modelPoints.length > 0) {
        console.log(`🤖 deepseek-chat 模型消耗: ${modelPoints[0].points_per_request} 积分`);
      } else {
        console.log('⚠️ 未找到 deepseek-chat 模型配置');
      }
    } catch (error) {
      console.error('❌ 模型积分查询失败:', error.message);
    }

    // 5. 检查系统设置
    try {
      const [settings] = await connection.execute(
        'SELECT setting_key, setting_value FROM system_settings WHERE category = "points"'
      );
      
      console.log('⚙️ 积分系统设置:');
      settings.forEach(setting => {
        console.log(`  ${setting.setting_key}: ${setting.setting_value}`);
      });
    } catch (error) {
      console.error('❌ 系统设置检查失败:', error.message);
    }

    console.log('\n✅ 快速测试完成');

  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

quickTest().catch(console.error);
