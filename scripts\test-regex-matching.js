// 测试正则表达式匹配
function testRegexMatching() {
  console.log('🔍 测试正则表达式匹配...\n');
  
  const testDescriptions = [
    'AI模型 doubao-seed-1-6-250615 请求消耗',
    'AI模型 deepseek-chat 请求消耗',
    'AI模型doubao-seed-1-6-250615请求消耗', // 没有空格
    'AI模型  doubao-seed-1-6-250615  请求消耗', // 多个空格
  ];
  
  const MODEL_DISPLAY_MAPPING = {
    'doubao-seed-1-6-250615': 'LoomRun 1.6DB',
    'deepseek-chat': 'LoomRun 1.2DS',
  };
  
  const getDisplayModelName = (originalModelName) => {
    if (MODEL_DISPLAY_MAPPING[originalModelName]) {
      return MODEL_DISPLAY_MAPPING[originalModelName];
    }
    
    if (originalModelName.includes('doubao') || originalModelName.includes('ep-')) {
      return 'LoomRun 1.6DB';
    }
    
    if (originalModelName.includes('deepseek')) {
      return 'LoomRun 1.2DS';
    }
    
    return originalModelName;
  };
  
  testDescriptions.forEach((description, index) => {
    console.log(`📋 测试 ${index + 1}: ${description}`);
    
    // 方法1: 使用metadata中的model_key进行替换
    const mockMetadata = { model_key: 'doubao-seed-1-6-250615' };
    let result1 = description;
    if (mockMetadata?.model_key) {
      const displayModelName = getDisplayModelName(mockMetadata.model_key);
      result1 = result1.replace(
        /AI模型\s+[\w\-\.]+/g,
        `AI模型 ${displayModelName}`
      );
    }
    console.log(`  方法1 (metadata): ${result1}`);
    
    // 方法2: 直接字符串替换
    let result2 = description
      .replace(/doubao-seed-1-6-250615/g, 'LoomRun 1.6DB')
      .replace(/doubao-[\w\-]+/g, 'LoomRun 1.6DB')
      .replace(/deepseek-[\w\-]+/g, 'LoomRun 1.2DS')
      .replace(/ep-[\w\-]+/g, 'LoomRun 1.6DB');
    console.log(`  方法2 (直接替换): ${result2}`);
    
    // 方法3: 更精确的正则匹配
    let result3 = description;
    const modelMatch = description.match(/AI模型\s*([\w\-\.]+)/);
    if (modelMatch) {
      const originalModel = modelMatch[1];
      const displayModel = getDisplayModelName(originalModel);
      result3 = description.replace(modelMatch[0], `AI模型 ${displayModel}`);
    }
    console.log(`  方法3 (精确匹配): ${result3}`);
    
    console.log('');
  });
  
  // 测试当前API中使用的正则表达式
  console.log('🔍 测试当前API中的正则表达式:');
  const currentRegex = /AI模型\s+[\w\-\.]+/g;
  
  testDescriptions.forEach((desc, index) => {
    const matches = desc.match(currentRegex);
    console.log(`描述 ${index + 1}: ${desc}`);
    console.log(`  匹配结果: ${matches ? matches.join(', ') : '无匹配'}`);
    console.log('');
  });
}

testRegexMatching();
